export const providers: Record<
  string,
  { name: string; baseURL: string; envKey: string }
> = {
  openai: {
    name: "OpenAI",
    baseURL: "https://api.openai.com/v1",
    envKey: "OPENAI_API_KEY",
  },
  openrouter: {
    name: "OpenRouter",
    baseURL: "https://openrouter.ai/api/v1",
    envKey: "OPENROUTER_API_KEY",
  },
  azure: {
    name: "AzureOpenAI",
    baseURL: "https://YOUR_PROJECT_NAME.openai.azure.com/openai",
    envKey: "AZURE_OPENAI_API_KEY",
  },
  gemini: {
    name: "<PERSON>",
    baseURL: "https://generativelanguage.googleapis.com/v1beta/openai",
    envKey: "GEMINI_API_KEY",
  },
  ollama: {
    name: "Ollama",
    baseURL: "http://localhost:11434/v1",
    envKey: "OLLAMA_API_KEY",
  },
  mistral: {
    name: "Mistra<PERSON>",
    baseURL: "https://api.mistral.ai/v1",
    envKey: "MISTRAL_API_KEY",
  },
  deepseek: {
    name: "DeepSeek",
    baseURL: "https://api.deepseek.com",
    envKey: "DEEPSEEK_API_KEY",
  },
  xai: {
    name: "xAI",
    baseURL: "https://api.x.ai/v1",
    envKey: "XAI_API_KEY",
  },
  groq: {
    name: "Groq",
    baseURL: "https://api.groq.com/openai/v1",
    envKey: "GROQ_API_KEY",
  },
  arceeai: {
    name: "ArceeAI",
    baseURL: "https://conductor.arcee.ai/v1",
    envKey: "ARCEEAI_API_KEY",
  },
};
